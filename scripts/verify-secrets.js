#!/usr/bin/env node

/**
 * GitHub Secrets 驗證腳本
 * 用於檢查 CI/CD 所需的環境變數是否正確設定
 */

const requiredSecrets = {
  // PayUni 測試環境
  'PAYUNI_SANDBOX_MER_ID': {
    description: 'PayUni 測試商店 ID',
    example: 'S01421169',
    required: true
  },
  'PAYUNI_SANDBOX_HASH_KEY': {
    description: 'PayUni 測試 Hash Key (32字節)',
    example: '****************7890123456789012',
    required: true,
    validate: (value) => value && value.length === 32
  },
  'PAYUNI_SANDBOX_HASH_IV': {
    description: 'PayUni 測試 Hash IV (16字節)',
    example: '****************',
    required: true,
    validate: (value) => value && value.length === 16
  },
  'PAYUNI_NOTIFY_URL': {
    description: 'PayUni 通知回調 URL',
    example: 'https://your-domain.com/api/webhook/payment',
    required: true
  },

  // Google Sheets 測試環境
  'GOOGLE_SERVICE_ACCOUNT_EMAIL': {
    description: 'Google Service Account Email',
    example: '<EMAIL>',
    required: true
  },
  'GOOGLE_PRIVATE_KEY': {
    description: 'Google Service Account Private Key',
    example: '-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n',
    required: true,
    validate: (value) => value && value.includes('BEGIN PRIVATE KEY')
  },
  'GOOGLE_SANDBOX_SHEET_ID': {
    description: 'Google Sheets 測試表單 ID',
    example: '1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms',
    required: true
  },
  'GOOGLE_SANDBOX_WATCH_SHEET_ID': {
    description: 'Google Sheets 錶款測試表單 ID',
    example: '1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms',
    required: false
  },
  'GOOGLE_SANDBOX_BLOG_SHEET_ID': {
    description: 'Google Sheets 部落格測試表單 ID',
    example: '1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms',
    required: false
  },

  // Meta Pixel 測試環境
  'META_SANDBOX_PIXEL_ID': {
    description: 'Meta Pixel 測試 ID',
    example: '****************',
    required: false
  },
  'META_SANDBOX_ACCESS_TOKEN': {
    description: 'Meta CAPI 測試 Access Token',
    example: 'EAABwzLixnjYBOZC...',
    required: false
  },
  'META_SANDBOX_TEST_EVENT_CODE': {
    description: 'Meta 測試事件代碼',
    example: 'TEST12345',
    required: false
  }
};

function checkEnvironmentVariables() {
  console.log('🔍 檢查環境變數設定...\n');
  
  let allValid = true;
  const results = [];

  for (const [key, config] of Object.entries(requiredSecrets)) {
    const value = process.env[key];
    const isSet = Boolean(value);
    const isValid = !config.validate || config.validate(value);
    
    let status = '✅';
    let message = '已設定且有效';
    
    if (!isSet) {
      if (config.required) {
        status = '❌';
        message = '未設定 (必要)';
        allValid = false;
      } else {
        status = '⚠️';
        message = '未設定 (可選)';
      }
    } else if (!isValid) {
      status = '❌';
      message = '已設定但格式無效';
      allValid = false;
    }
    
    results.push({
      key,
      status,
      message,
      description: config.description,
      example: config.example,
      required: config.required
    });
  }

  // 顯示結果
  console.log('📊 環境變數檢查結果:\n');
  results.forEach(result => {
    console.log(`${result.status} ${result.key}`);
    console.log(`   描述: ${result.description}`);
    console.log(`   狀態: ${result.message}`);
    if (!process.env[result.key] && result.required) {
      console.log(`   範例: ${result.example}`);
    }
    console.log('');
  });

  // 總結
  if (allValid) {
    console.log('🎉 所有必要的環境變數都已正確設定！');
    console.log('✅ CI/CD 測試應該可以正常執行。');
  } else {
    console.log('⚠️  有些必要的環境變數未設定或無效。');
    console.log('❌ CI/CD 測試可能會失敗。');
    console.log('\n📝 請在 GitHub 儲存庫設定中新增缺少的 Secrets:');
    console.log('   Settings → Secrets and variables → Actions → New repository secret');
  }

  return allValid;
}

function generateSecretsTemplate() {
  console.log('\n📋 GitHub Secrets 設定範本:\n');
  console.log('請將以下內容複製到您的 GitHub Secrets 設定中:\n');
  
  Object.entries(requiredSecrets).forEach(([key, config]) => {
    console.log(`# ${config.description}`);
    console.log(`${key}=${config.example}`);
    console.log('');
  });
}

// 主程式
if (require.main === module) {
  console.log('🔐 GitHub Secrets 驗證工具\n');
  
  const isValid = checkEnvironmentVariables();
  
  if (!isValid) {
    generateSecretsTemplate();
    process.exit(1);
  }
  
  process.exit(0);
}

module.exports = {
  checkEnvironmentVariables,
  requiredSecrets
};
