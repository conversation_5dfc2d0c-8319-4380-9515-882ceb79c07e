{"name": "pangea-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest --testPathPattern=unit", "test:integration": "jest --testPathPattern=integration", "test:all": "npm run test:unit && npm run test:integration", "test:api": "node src/__tests__/run-api-tests.js", "test:api:suite": "node src/__tests__/run-api-tests.js suite", "test:api:coverage": "node src/__tests__/run-api-tests.js coverage", "test:api:quick": "node src/__tests__/run-api-tests.js quick", "test:api:forms": "node src/__tests__/run-api-tests.js suite form-submission", "test:api:payuni": "node src/__tests__/run-api-tests.js suite payuni-integration", "test:api:sheets": "node src/__tests__/run-api-tests.js suite google-sheets", "test:api:edge": "node src/__tests__/run-api-tests.js suite edge-cases"}, "dependencies": {"@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "googleapis": "^150.0.1", "lucide-react": "^0.525.0", "next": "15.3.4", "node-fetch": "^3.3.2", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@jest/globals": "^29.7.0", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "msw": "^2.10.3", "tailwindcss": "^4", "typescript": "^5"}}