# 環境變數配置指南

## 📋 當前環境變數配置狀態

### ✅ 已正確配置的項目

#### 1. PayUni 付款系統
```bash
# 環境控制
PAYUNI_ENVIRONMENT=sandbox  # 或 production

# 測試環境憑證
PAYUNI_SANDBOX_MER_ID=S01421169
PAYUNI_SANDBOX_HASH_KEY=********************************
PAYUNI_SANDBOX_HASH_IV=2fjTDwg7OCFid91U

# 正式環境憑證
PAYUNI_PRODUCTION_MER_ID=SHOP6220811892463388
PAYUNI_PRODUCTION_HASH_KEY=bWoacqmMTH1o14jJHSWsgBIy3u5KKqbI
PAYUNI_PRODUCTION_HASH_IV=DeN566gVaWyrjvbh
```

#### 2. Google Sheets 整合
```bash
# 服務帳戶憑證（所有環境共用）
GOOGLE_SERVICE_ACCOUNT_EMAIL=<EMAIL>
GOOGLE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n"

# 不同功能的 Sheet ID
GOOGLE_SHEET_ID=1STjvYv5a8BFJS8v0sdcsHjoqoe4QgEPnjgNNk4qZST8          # 主要報名系統
GOOGLE_WATCH_SHEET_ID=1qloy7XgaGuht4zXr7-bl0FFCXyq-lB6fVMtNWJv64NY      # 手錶資料
GOOGLE_BLOG_SHEET_ID=1yh7vaI5HP2kgJGQu_OnbIz__ZfNexf5ZleHqTiyOhVg       # 部落格文章
```

### 🔄 建議優化的項目

#### 1. Google Sheets 環境區分
**建議：** 為測試環境創建獨立的 Google Sheets

```bash
# 建議的環境區分配置
PAYUNI_ENVIRONMENT=sandbox  # 或 production（與當前使用的變數名稱一致）

# 開發/測試環境 Sheet ID
DEV_GOOGLE_SHEET_ID=test_sheet_id_for_development
DEV_GOOGLE_WATCH_SHEET_ID=test_watch_sheet_id
DEV_GOOGLE_BLOG_SHEET_ID=test_blog_sheet_id

# 正式環境 Sheet ID（當前使用的）
PROD_GOOGLE_SHEET_ID=1STjvYv5a8BFJS8v0sdcsHjoqoe4QgEPnjgNNk4qZST8
PROD_GOOGLE_WATCH_SHEET_ID=1qloy7XgaGuht4zXr7-bl0FFCXyq-lB6fVMtNWJv64NY
PROD_GOOGLE_BLOG_SHEET_ID=1yh7vaI5HP2kgJGQu_OnbIz__ZfNexf5ZleHqTiyOhVg
```

#### 2. Meta Pixel 環境區分
**建議：** 區分測試和正式環境的 Meta Pixel

```bash
# 當前配置（測試環境）
META_PIXEL_ID=3369341296705156
META_ACCESS_TOKEN=EAAMJz9Wz9CkBPIQZB6HShYqZCoVwLGQMZA0EEERfrkyUkX4O56qhGviDvIHOixJbDJC9ZBjUnNmUeimGHCGDZCnvYDqcClQsnMCw27THOlQDVki2ayWKyBKQ04GYoCnf98xJBzJTRuQOPNZBmL6ZAmzyMXNhEuLNFJi1ZBzlnhdjAn9KefjZAEMZC2nsLFDiKSMfMx3AZDZD
META_TEST_EVENT_CODE=TEST15370

# 建議新增正式環境配置
PROD_META_PIXEL_ID=正式環境的_pixel_id
PROD_META_ACCESS_TOKEN=正式環境的_access_token
```

#### 3. GTM 環境配置
**建議：** 設定正式環境的 GTM ID

```bash
# 當前配置（測試）
NEXT_PUBLIC_GTM_ID=GTM-TEST123

# 建議新增正式環境配置
NEXT_PUBLIC_PROD_GTM_ID=GTM-XXXXXXX  # 正式環境的 GTM ID
```

### ❌ 已移除的配置

#### 1. 常見問題 Google Sheets
```bash
# 已註解，因為 FAQ 改用本地檔案管理
# GOOGLE_QUESTION_SHEET_ID=17XqpdB1j_qoJNSojb7xPrEXGcNiJdZZMx1Om5VBujjY
```

### 📁 .env.test 檔案評估

#### 當前用途
- 提供測試專用的模擬配置
- 避免測試時影響正式環境資料
- 包含安全的測試憑證

#### 建議保留原因
1. **測試隔離**：確保測試不會影響正式環境
2. **CI/CD 整合**：GitHub Actions 可以使用測試配置
3. **開發安全**：避免意外使用正式環境憑證

#### 建議優化
```bash
# 建議的 .env.test 配置
NODE_ENV=test

# PayUni 測試配置
PAYUNI_ENVIRONMENT=sandbox
PAYUNI_SANDBOX_MERCHANT_ID=S01421169
PAYUNI_SANDBOX_HASH_KEY=test_hash_key_for_testing
PAYUNI_SANDBOX_HASH_IV=test_hash_iv_for_testing

# Google Sheets 測試配置
GOOGLE_SERVICE_ACCOUNT_EMAIL=<EMAIL>
GOOGLE_PRIVATE_KEY=-----BEGIN PRIVATE KEY-----\nTEST_PRIVATE_KEY\n-----END PRIVATE KEY-----
GOOGLE_SHEET_ID=test_sheet_id_for_testing

# Meta CAPI 測試配置
META_PIXEL_ID=test_pixel_id
META_ACCESS_TOKEN=test_access_token

# 測試控制開關
TEST_MODE=true
SKIP_EXTERNAL_API_CALLS=true
MOCK_GOOGLE_SHEETS=true
MOCK_PAYUNI_API=true
```

## 🚀 實施建議

### 優先級 1：立即實施
1. ✅ 移除不再使用的 `GOOGLE_QUESTION_SHEET_ID`
2. 🔄 優化 `.env.test` 配置，增加更多測試控制開關

### 優先級 2：中期實施
1. 為測試環境創建獨立的 Google Sheets
2. 設定正式環境的 GTM ID
3. 評估是否需要正式環境的 Meta Pixel

### 優先級 3：長期考慮
1. 實施完整的環境配置管理系統
2. 考慮使用 Docker 環境變數管理
3. 實施配置驗證機制

## 📊 成本影響評估

### Vercel 使用量影響
- **環境變數數量**：不會影響 Vercel 限制
- **API 呼叫**：測試環境隔離可減少正式環境 API 使用量
- **資料傳輸**：測試資料不會計入正式環境流量

### 維護成本
- **低**：環境變數管理相對簡單
- **中**：需要維護測試環境的 Google Sheets
- **低**：Meta Pixel 和 GTM 配置一次性設定
